from fastapi import FastAP<PERSON>
from pydantic import BaseModel
from typing import Optional
import asyncio,os
from dotenv import load_dotenv
import logging
from models import PersistanceAdapter, TBot
from fastapi.responses import PlainTextResponse

class Customer(BaseModel):
    arg1: str
    arg2: str

WORKING_DIRECTORY = os.path.dirname(os.path.realpath(__file__))
load_dotenv(dotenv_path=os.path.join(WORKING_DIRECTORY, ".env"))

app = FastAPI()

@app.get("/status")
def status():
    return {"status":"online"}

@app.get("/checkPin/",response_class=PlainTextResponse)
def create_item(arg1: str, arg2: str):
    try:
        db = PersistanceAdapter()
        tbot = TBot(os.getenv("token"))
        customer = db.getCostumer(arg2)
    except Exception as e:
        logging.error(f"AppError: {repr(e)}")
        return """<?xml version="1.0"?>
                    <response>
                        <displayprefix>Not Found</displayprefix>
                        <value>404</value>
                    </response>"""
    if(customer is not None):
        try:
            logging.info(f"Adding alarm for: "+customer["Name"])
            db.addAlarm(f"Chiamata Ricevuta da **{customer["Name"]}**")
            asyncio.run(tbot.sendAlert())
            return f"""<?xml version="1.0"?>
                    <response>
                        <displayprefix>{customer["Name"]}</displayprefix>
                        <value>200</value>
                    </response>"""
        except Exception as e:
            logging.error(f"AppError sending alert: {repr(e)}")
    return """<?xml version="1.0"?>
                <response>
                    <displayprefix>Not Found</displayprefix>
                    <value>404</value>
                </response>"""