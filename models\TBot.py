from models.PersistanceAdapter import PersistanceAdapter
from telegram import Update,Bot,ReplyKeyboardMarkup,Chat,InlineKeyboardMarkup,InlineKeyboardButton,KeyboardButton
from telegram.ext import <PERSON><PERSON>uilder,CallbackQueryHandler, CommandHandler, ContextTypes,ConversationHandler,MessageHandler,filters
from threading import Thread
import asyncio
import time 

class TBot:
    SENDPIN, SENDINTERNAL, DONE= map(str, range(0, 3))
    def __init__(self,token,register_pin="99292"):
        self.EMOJI = {
            'check': '✅',
            'error': '❌',
            'warning': '⚠️'
        }
        self.db = PersistanceAdapter()
        self.token = token
        self.app = ApplicationBuilder().token(self.token).build()
        self.register_pin = register_pin
        # Set conversation handler for /register command
        self.conv_handler = ConversationHandler(
            entry_points=[CommandHandler("registrati", self.register)],
            states={
                self.SENDPIN: [
                    MessageHandler(callback=self.sendpin,filters=filters.TEXT)
                ],
                self.SENDINTERNAL: [
                    MessageHandler(callback=self.sendinternumber,filters=filters.TEXT)
                ],
            },
            fallbacks=[CommandHandler("cancel", self.cancel)],
        )
        #self.db.initDB()
        self.app.add_handler(self.conv_handler)
        self.app.add_handler(CommandHandler("start", self.start))
        self.app.add_handler(CommandHandler("reperibile", self.enableOnShift))
        self.app.add_handler(CommandHandler("fineturno", self.disableOnShift))
        self.app.add_handler(CallbackQueryHandler(self.ack_alarm))

    def startBot(self):
        self.app.run_polling(allowed_updates=Update.ALL_TYPES)
    async def register(self,update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        print("register")
        await update.message.reply_text(f'Ok {update.effective_user.first_name} send me the pin to register!')
        return self.SENDPIN

    async def sendpin(self,update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        print("sendpin")
        if(update.message.text==self.register_pin):
            await update.message.reply_text(f'Ok {update.effective_user.first_name} the PIN is ok!\nSend me you mobile number where to call you (without +39):')
            return self.SENDINTERNAL
        else:
            await update.message.reply_text(f'Bad PIN! Retry')
            return self.SENDPIN

    async def sendinternumber(self,update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        print(update.message.text)
        if(self.db.getTechnician(chat_id=update.message.chat.id) == None):
            print("Technician not registered")
            self.db.addTechnician(chat_id=update.message.chat.id, phone=update.message.text,name=update.effective_user.full_name)
        else:
            print("Technician already registered")
            self.db.updateTechnician(chat_id=update.message.chat.id, phone=update.message.text,name=update.effective_user.full_name)
        await update.message.reply_text(f'Ok {update.effective_user.first_name} you have been registered! with phone number {update.message.text}!')
        return ConversationHandler.END

    async def enableOnShift(self,update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        print("enableOnShift")
        self.db.setShiftTechnician(id=update.message.chat.id, isOnShift=True)
        await update.message.reply_text(f'Ok {update.effective_user.first_name} you are on shift!')

    async def disableOnShift(self,update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        print("disableOnShift")
        self.db.setShiftTechnician(id=update.message.chat.id, isOnShift=False)
        await update.message.reply_text(f'Ok {update.effective_user.first_name} you are out of shift!')
    
    async def cancel(self,update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        print("cancel")
        await update.message.reply_text("Canceled!")

    async def sendAlert(self) -> None:
        bot = Bot(token=self.token)
        techs= self.db.getTechnicians(type="active")
        for tech in techs:
            for alarm in self.db.getActiveAlarms():
                keyboard = [
                    [InlineKeyboardButton("ACK", callback_data=str(alarm["id"]))]
                ]
                reply_markup = InlineKeyboardMarkup(keyboard)
                await bot.send_message(chat_id=tech['chat_id'], text=f'{self.EMOJI["warning"]} There is a new alarm {alarm["message"]}',reply_markup=reply_markup)
    async def sendCustomAlert(self,message,type="active",emoji="warning") -> None:
        bot = Bot(token=self.token)
        techs= self.db.getTechnicians(type=type)
        print(techs)   
        for tech in techs:
                await bot.send_message(chat_id=tech['chat_id'], text=f'{self.EMOJI[emoji]} {message}')
    async def ack_alarm(self,update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        bot = Bot(token=self.token)
        query = update.callback_query
        await query.answer()
        alarm=self.db.getAlarm(query.data)
        self.db.setAcknowledgedAlarm(id=int(query.data))
        techs=self.db.getTechnicians()
        # remove button when acknowledge
        await query.edit_message_text(text=update.callback_query.message.text,reply_markup=None)
        for tech in techs:
            await  bot.send_message(chat_id=tech['chat_id'],text=f"{self.EMOJI['check']} Alarm {alarm['message']} with id: {query.data} ACKnowledged by: {update.effective_user.first_name}")

    async def start(self,update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        keyboard = [
            [   
                KeyboardButton("/reperibile"),
                KeyboardButton("/fineturno"),
            ],
            [KeyboardButton("/registrati")]
        ]
        reply_markup = ReplyKeyboardMarkup(keyboard)
        await update.message.reply_text("Benvenuto!Se non ti sei ancora registrato /registrati", reply_markup=reply_markup)
