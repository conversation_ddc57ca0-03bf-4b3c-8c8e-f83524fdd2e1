import schedule
import asyncio
import time
import logging
from datetime import datetime, timedelta
from models import PersistanceAdapter,TBot
class ScheduleManager:
    def __init__(self,token,start_time,end_time,phoneAlarm=3,retryInterval=5):
        self._token = token
        self._phoneAlarm = phoneAlarm
        self._db = PersistanceAdapter()
        self._start_time = start_time
        self._end_time = end_time
        self._retryInterval=retryInterval
        self._tbot = TBot(token)
    def _isServiceActive(self):
        current_time = datetime.now().time()
        start_time = datetime.strptime(f"{self._start_time}:00", "%H:%M").time()
        end_time = datetime.strptime(f"{self._end_time}:00", "%H:%M").time()
        if(current_time>start_time and current_time<end_time):
            return True
        return False
    def SendAlarm(self):
        logging.info("ScheduleManager: SendAlarm Started")
        alarms = self._db.getActiveAlarms()
        for a in alarms:
            asyncio.run(self._tbot.sendAlert())
            self._db.updateAlarmRetries(a["id"],a["retries"]+1)
            if(a["retries"]<self._phoneAlarm):
                print("Qui inizierei la chiamata con la KCN")
    def checkOnShift(self):
        logging.info("ScheduleManager: checkOnShift Started")
        if(self._isServiceActive()):
            logging.info("ScheduleManager: checkOnShift Started -> Service is active")
            techs = self._db.getTechnicians()
            if(len(techs)==0):
                logging.warning("ScheduleManager: checkOnShift Started -> No technician on shift")
                asyncio.run(self._tbot.sendCustomAlert("Attenzione! Nessun Tecnico attivo per la reperibilità!\nEsegui /reperibile per entrare in turno!",type="all",emoji="error"))

    def start(self):
        schedule.every(2).minutes.do(self.checkOnShift)
        schedule.every(self._retryInterval).minutes.do(self.SendAlarm)
        #schedule.every(10).seconds.do(self.checkOnShift)
        #schedule.every(5).seconds.do(self.SendAlarm)
        while True:
            schedule.run_pending()