from models import PersistanceAdapter,TBot,ScheduleManager
import time
import asyncio
import os
import signal
from multiprocessing import Process
from dotenv import load_dotenv
import logging

def handle_signal(signum, frame):
   print(f"Signal received starting to stop!")
   for process in subProcesses.values():
       process.kill()
   stopExec = True

# Setting the handler for SIGINT
signal.signal(signal.SIGINT, handle_signal)

def StartBotWrapper():
    bot = TBot(token = os.getenv("token"),register_pin=str(os.getenv("botPin")))
    logging.info("TBot: Started")
    bot.startBot()
def StartSchedulerWrapper():
    scheduler = ScheduleManager(token=os.getenv("token"),
                                start_time=os.getenv("start_time"),
                                end_time=os.getenv("end_time"),
                                phoneAlarm=int(os.getenv("phoneAlarm")),
                                retryInterval=int(os.getenv("retryInterval")),
                                api_centrex=os.getenv("api_centrex"),
                                tenant=os.getenv("tenant"),
                                salt_tenant=os.getenv("salt_tenant"),
                                username=os.getenv("apiUsername_tenant"),
                                password=os.getenv("apiPassword_tenant")
                                )
    logging.info("ScheduleManager: Started")
    scheduler.start()


logging.basicConfig(level=logging.INFO)
stopExec = False
# Load environment variables from .env file
WORKING_DIRECTORY = os.path.dirname(os.path.realpath(__file__))
load_dotenv(dotenv_path=os.path.join(WORKING_DIRECTORY, ".env"))

if(not os.path.exists(WORKING_DIRECTORY+"/alarm.db")):
    PersistanceAdapter().initDB()
# Starting all subprocesses
subProcesses = {}
subProcesses["bot"] = Process(target=StartBotWrapper)
subProcesses["bot"].start()
subProcesses["ScheduleManager"] = Process(target=StartSchedulerWrapper)
subProcesses["ScheduleManager"].start()

while stopExec:
    time.sleep(5)