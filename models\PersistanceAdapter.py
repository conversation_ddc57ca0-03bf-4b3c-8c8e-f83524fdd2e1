import sqlite3
import os
class PersistanceAdapter:
	def __init__(self):
		self._con = sqlite3.connect("alarm.db")
		self._con.row_factory = sqlite3.Row
		self._cur = self._con.cursor()
	def initDB(self):
		self._con = sqlite3.connect("alarm.db")
		self._cur.execute("""CREATE TABLE `technician` (`chat_id` INT(20) PRIMARY KEY,`name` VARCHAR(30),`phone` VARCHAR(9),`isActive` BOOLEAN(20),`isOnShift` BOOLEAN(20));"""
		)
		self._cur.execute("""CREATE TABLE `alarm` (`id` INTEGER PRIMARY KEY AUTOINCREMENT, `message` TEXT(20),`retries` INT,`acknowledged` BOOLEAN(20));"""
		)
		self._cur.execute("""CREATE TABLE `customer` (`id` INTEGER PRIMARY KEY AUTOINCREMENT,`Name` VARCHAR(20),`pin` VARCHAR(10));"""
		)
	def addTechnician(self, chat_id, phone,name):
		self._cur.execute("""INSERT INTO technician (name,chat_id, phone, isActive, isOnShift) VALUES (?,?,?,?,?)""", (name, chat_id, phone, False, False))
		self._con.commit()
	
	def getTechnician(self, chat_id):
		self._cur.execute("""SELECT * FROM technician WHERE chat_id=?""", (chat_id,))
		return self._cur.fetchone()

	def updateTechnician(self, chat_id, phone, name):
		self._cur.execute("""UPDATE technician SET name=?, phone=? WHERE chat_id=?""", (name, phone, chat_id))
		self._con.commit()
	def setShiftTechnician(self, id, isOnShift):
		isActive = False
		if(isOnShift):
			isActive = True
		self._cur.execute("""UPDATE technician SET isOnShift=?, isActive=? WHERE chat_id=?""", (isOnShift,isActive,id))
		self._con.commit()
	def setActiveTechnician(self, id, isActive):
		self._cur.execute("""UPDATE technician SET isActive=? WHERE chat_id=?""", (isActive,id))
		self._con.commit()
	def getTechnicians(self,type="onShift"):
		match type:
			case "all":
				self._cur.execute("""SELECT * FROM technician""")
			case "onShift":
				self._cur.execute("""SELECT * FROM technician WHERE isActive=1 AND isOnShift=1""")
			case "active":
				self._cur.execute("""SELECT * FROM technician WHERE isActive=1""")
		return self._cur.fetchall()
	
	def getActiveAlarms(self):
		self._cur.execute("""SELECT * FROM alarm WHERE acknowledged=0""")
		return self._cur.fetchall()
	def addAlarm(self, message):
		self._cur.execute("""INSERT INTO alarm (message, retries, acknowledged) VALUES (?,0,0)""", (message,))
		self._con.commit()
	def updateAlarmRetries(self, id, retries):
		self._cur.execute("""UPDATE alarm SET retries=? WHERE id=?""", (retries, id))
		self._con.commit()
	
	def getCostumer(self, pin):
		self._cur.execute("""SELECT * FROM customer WHERE pin=?""", (pin,))
		return self._cur.fetchone()

	def getAlarm(self,id):
		self._cur.execute("""SELECT * FROM alarm WHERE id=?""", (id,))
		return self._cur.fetchone()
	def setAcknowledgedAlarm(self, id):
		self._cur.execute("""UPDATE alarm SET acknowledged=1 WHERE id=?""", (id,))
		self._con.commit()