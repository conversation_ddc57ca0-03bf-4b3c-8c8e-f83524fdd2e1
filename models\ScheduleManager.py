import schedule
import asyncio
import time
import logging
import requests
import hashlib
import secrets
from datetime import datetime, timedelta
from models import PersistanceAdapter,TBot
class ScheduleManager:
    def __init__(self,token,start_time,end_time,api_centrex,tenant,salt_tenant,username,password,phoneAlarm=3,retryInterval=5):
        self._token = token
        self._phoneAlarm = phoneAlarm
        self._db = PersistanceAdapter()
        self._start_time = start_time
        self._end_time = end_time
        self._retryInterval=retryInterval
        self._tbot = TBot(token)
        self._api_centrex = api_centrex
        self._tenant = tenant
        self._salt_tenant = salt_tenant
    
    def _isServiceActive(self):
        current_time = datetime.now().time()
        start_time = datetime.strptime(f"{self._start_time}:00", "%H:%M").time()
        end_time = datetime.strptime(f"{self._end_time}:00", "%H:%M").time()
        midnight = datetime.strptime("00:00", "%H:%M").time()
        if((start_time < current_time and current_time < midnight) or (midnight < current_time and current_time < end_time)):
            created = str(int(time.time()))
            return True
        return False
    
    def _generateRequestHeader(self):
        created = str(int(time.time()))
        nonce = hashlib.md5((created + str(random.randint(100000, 999999)) + str(random.random())).encode()).hexdigest()
        
        return header
    
    def _callerTecnician(self,technician):
        logging.info(f"ScheduleManager: _callerTecnician call technician {technician['phone']}")
        try:
            request = requests.get(f"https://{self._api_centrex}",
            headers={
                "Authorization": f"Bearer {self._salt_tenant}",
                "Content-Type": "application/json"
                })
            technician["called"] = True 
        except Exception as e:
            logging.error(f"ScheduleManager: _callerTecnician call technician -> {repr(e)}")
    
    def SendAlarm(self):
        logging.info("ScheduleManager: SendAlarm Started")
        if(self._isServiceActive()):
            logging.info("ScheduleManager: SendAlarm Started -> Service is not active")
            return
        try:
            asyncio.run(self._tbot.sendAlert())
            alarms = self._db.getActiveAlarms()
            technicians = self._db.getTechnicians()
        except Exception as e:
            logging.error(f"ScheduleManager: SendAlarm -> {repr(e)}")
            return
    
        for a in alarms:
            try:
                if(a["retries"]>self._phoneAlarm):
                    for t in technicians:
                        if not "called" in t:
                            # Call the technician                                                  
                            self._callerTecnician()

                self._db.updateAlarmRetries(a["id"],a["retries"]+1)
            except Exception as e:
                logging.error(f"ScheduleManager: SendAlarm -> {repr(e)}")
            
    def checkOnShift(self):
        logging.info("ScheduleManager: checkOnShift Started")
        if(self._isServiceActive()):
            logging.info("ScheduleManager: checkOnShift Started -> Service is active")
            try:
                techs = self._db.getTechnicians()
                if(len(techs)==0):
                    logging.warning("ScheduleManager: checkOnShift Started -> No technician on shift")
                    asyncio.run(self._tbot.sendCustomAlert("Attenzione! Nessun Tecnico attivo per la reperibilità!\nEsegui /reperibile per entrare in turno!",type="all",emoji="error"))
            except Exception as e:
                logging.error(f"ScheduleManager: checkOnShift -> {repr(e)}")

    def start(self):
        schedule.every(2).minutes.do(self.checkOnShift)
        schedule.every(self._retryInterval).minutes.do(self.SendAlarm)
        #schedule.every(10).seconds.do(self.checkOnShift)
        #schedule.every(5).seconds.do(self.SendAlarm)
        while True:
            schedule.run_pending()
            time.sleep(10)