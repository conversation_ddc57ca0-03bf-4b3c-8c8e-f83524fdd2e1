from fastapi import FastAPI
from pydantic import BaseModel
from typing import Optional

class NotificationDataModel(BaseModel):
    customer: str
    description: str | None = None

# Create FastAPI instance
app = FastAPI()



# Root endpoint
@app.get("/")
def read_root():
    return {"status":"online"}

# Create a new item
@app.post("/sendAlarm/")
def create_item(item: NotificationDataModel):
    print(item)
    #invoke telegram

    return {"result":"done"}

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000)