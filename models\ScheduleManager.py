import schedule
import asyncio
import time
import logging
import requests
import hashlib
import secrets
import base64
from datetime import datetime, timedelta
from models import PersistanceAdapter,TBot
class ScheduleManager:

    def __init__(self,token,start_time,end_time,tenant,salt_tenant,username,password,phoneAlarm=3,retryInterval=5):
        self._token = token
        self._phoneAlarm = phoneAlarm
        self._db = PersistanceAdapter()
        self._start_time = start_time
        self._end_time = end_time
        self._retryInterval=retryInterval
        self._tbot = TBot(token)
        self._api_centrex = "api.centrex-pbx.welcomeitalia.it"
        self._tenant = tenant
        self._salt_tenant = salt_tenant
        self._username = username
        self._password = password
    
    def _isServiceActive(self):
        current_time = datetime.now().time()
        start_time = datetime.strptime(f"{self._start_time}:00", "%H:%M").time()
        end_time = datetime.strptime(f"{self._end_time}:00", "%H:%M").time()
        midnight = datetime.strptime("00:00", "%H:%M").time()
        if((start_time < current_time and current_time < midnight) or (midnight < current_time and current_time < end_time)):
            return True
        return False
    
    def _rand_hex(self, length):
        """Genera una stringa esadecimale casuale di lunghezza specificata"""
        return secrets.token_hex(length // 2)

    def _generate_x_authenticate_header(self):
        
        # Step 1: Genera digest password con salt
        digest_password = hashlib.sha256(f"{self._password}{{{self._salt_tenant}}}".encode()).hexdigest()
        logging.info(f'Digest password for X-authenticate header is "{digest_password}"')

        # Step 2: Genera nonce casuale (32 caratteri hex)
        nonce = self._rand_hex(32)
        logging.info(f'Generate X-authenticate header with random nonce "{nonce}"')

        # Step 3: Genera timestamp ISO con formato specifico
        created = datetime.now().isoformat()[:19] + 'Z'

        # Step 4: Genera digest finale
        digest_string = f"{nonce}{digest_password}{self._username}{self._tenant}{created}"
        digest_hash = hashlib.sha256(digest_string.encode()).digest()
        digest64 = base64.b64encode(digest_hash).decode()

        # Step 5: Costruisce l'header finale
        x_authenticate = f'RestApiUsernameToken Username="{self._username}", Domain="{self._tenant}", Digest="{digest64}", Nonce="{nonce}", Created="{created}"'
        logging.info(f'Generated X-authenticate header is: {x_authenticate}')

        return x_authenticate
    
    def _callerTecnician(self,technician):
        logging.info(f"ScheduleManager: _callerTecnician call technician {technician['phone']}")

        try:
            # Genera l'header di autenticazione
            x_authenticate_header = self._generate_x_authenticate_header()

            # Effettua la chiamata API con l'header corretto
            response = requests.get(f"https://{self._api_centrex}/kcnApiProxy/api/pbx/v2/phoneService/advCallback/{technician['phone']}/100/900",
                headers={
                    "X-authenticate": x_authenticate_header,
                    "Content-Type": "text/html"
                    },
                    verify=False)

            if response.status_code == 200:
                technician["called"] = True
                logging.info(f"ScheduleManager: _callerTecnician -> Chiamata effettuata con successo per {technician['phone']}")
            else:
                logging.error(f"ScheduleManager: _callerTecnician -> Errore HTTP {response.status_code}: {response.text}")

        except Exception as e:
            logging.error(f"ScheduleManager: _callerTecnician call technician -> {repr(e)}")
    
    def SendAlarm(self):
        logging.info("ScheduleManager: SendAlarm Started")
        if(self._isServiceActive()):
            logging.info("ScheduleManager: SendAlarm Started -> Service is not active")
            return
        try:
            asyncio.run(self._tbot.sendAlert())
            alarms = self._db.getActiveAlarms()
            technicians = self._db.getTechnicians()
        except Exception as e:
            logging.error(f"ScheduleManager: SendAlarm -> {repr(e)}")
            return
    
        for a in alarms:
            try:
                if(a["retries"]>self._phoneAlarm):
                    for t in technicians:
                        if not "called" in t:
                            # Call the technician                                                  
                            self._callerTecnician(t)

                self._db.updateAlarmRetries(a["id"],a["retries"]+1)
            except Exception as e:
                logging.error(f"ScheduleManager: SendAlarm -> {repr(e)}")
            
    def checkOnShift(self):
        logging.info("ScheduleManager: checkOnShift Started")
        if(self._isServiceActive()):
            logging.info("ScheduleManager: checkOnShift Started -> Service is active")
            try:
                techs = self._db.getTechnicians()
                if(len(techs)==0):
                    logging.warning("ScheduleManager: checkOnShift Started -> No technician on shift")
                    asyncio.run(self._tbot.sendCustomAlert("Attenzione! Nessun Tecnico attivo per la reperibilità!\nEsegui /reperibile per entrare in turno!",type="all",emoji="error"))
            except Exception as e:
                logging.error(f"ScheduleManager: checkOnShift -> {repr(e)}")

    def start(self):
        schedule.every(2).minutes.do(self.checkOnShift)
        schedule.every(self._retryInterval).minutes.do(self.SendAlarm)
        #schedule.every(10).seconds.do(self.checkOnShift)
        #schedule.every(5).seconds.do(self.SendAlarm)
        while True:
            schedule.run_pending()
            time.sleep(10)